# Archive Service Integration

Dokumentasi lengkap untuk integrasi Assessment Service dengan Archive Service.

## Overview

Assessment Service terintegrasi dengan Archive Service untuk:
1. **Job Management** - Membuat dan tracking analysis jobs
2. **Result Storage** - Menyimpan hasil analisis langsung (ji<PERSON> dip<PERSON>an)
3. **Status Synchronization** - Sinkronisasi status job antar services

## Perbaikan yang Dilakukan

### 1. **URL Configuration Fix**

**Sebelum:**
```javascript
const ARCHIVE_SERVICE_URL = process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002/archive';
```

**Sesudah:**
```javascript
const ARCHIVE_SERVICE_URL = process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002';
```

**Alasan:** Archive Service routes sudah menggunakan `/archive` prefix, jadi base URL tidak perlu include suffix.

### 2. **Endpoint Path Corrections**

<PERSON><PERSON>a endpoint calls sekarang menggunakan path yang benar:
- `/archive/jobs` - untuk job management
- `/archive/results` - untuk result management
- `/archive/health` - untuk health checks

### 3. **New Functionality Added**

#### Analysis Result Management
```javascript
// Create analysis result directly
await archiveService.createAnalysisResult(
  userId,
  assessmentData,
  personaProfile,
  'AI-Driven Talent Mapping',
  'completed'
);

// Get analysis result
const result = await archiveService.getAnalysisResult(resultId);

// Update analysis result
await archiveService.updateAnalysisResult(resultId, updateData);
```

#### Enhanced Error Handling
- Better timeout configuration (10 seconds)
- Improved error logging
- Proper HTTP status code handling

## API Methods

### Job Management

#### `createJob(jobId, userId, assessmentData, assessmentName)`
Membuat job baru di Archive Service.

**Parameters:**
- `jobId` (String) - Unique job identifier
- `userId` (String) - User UUID
- `assessmentData` (Object) - Assessment data (RIASEC, OCEAN, VIA-IS)
- `assessmentName` (String) - Assessment name (default: 'AI-Driven Talent Mapping')

**Returns:** Promise<Object> - Created job data

#### `getJobStatus(jobId)`
Mendapatkan status job dari Archive Service.

**Parameters:**
- `jobId` (String) - Job identifier

**Returns:** Promise<Object|null> - Job data or null if not found

#### `syncJobStatus(jobId, status, additionalData)`
Sinkronisasi status job dengan Archive Service.

**Parameters:**
- `jobId` (String) - Job identifier
- `status` (String) - New status
- `additionalData` (Object) - Additional data to sync

**Returns:** Promise<Object> - Sync response

### Result Management

#### `createAnalysisResult(userId, assessmentData, personaProfile, assessmentName, status, errorMessage)`
Membuat hasil analisis langsung di Archive Service.

**Parameters:**
- `userId` (String) - User UUID
- `assessmentData` (Object) - Assessment data
- `personaProfile` (Object) - Persona profile result
- `assessmentName` (String) - Assessment name
- `status` (String) - Result status ('completed', 'processing', 'failed')
- `errorMessage` (String) - Error message (optional, for failed status)

**Returns:** Promise<Object> - Created result data

#### `getAnalysisResult(resultId)`
Mendapatkan hasil analisis dari Archive Service.

**Parameters:**
- `resultId` (String) - Result UUID

**Returns:** Promise<Object|null> - Result data or null if not found

#### `updateAnalysisResult(resultId, updateData)`
Update hasil analisis di Archive Service.

**Parameters:**
- `resultId` (String) - Result UUID
- `updateData` (Object) - Data to update

**Returns:** Promise<Object> - Updated result data

### Health Check

#### `checkHealth()`
Cek kesehatan Archive Service.

**Returns:** Promise<Boolean> - Health status

## Environment Variables

```env
# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3002
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
```

## Usage Examples

### Basic Job Creation
```javascript
const archiveService = require('./src/services/archiveService');
const { v4: uuidv4 } = require('uuid');

const jobId = uuidv4();
const userId = uuidv4();
const assessmentData = {
  riasec: { realistic: 4.0, investigative: 3.5 },
  ocean: { openness: 4.2, conscientiousness: 3.8 }
};

const job = await archiveService.createJob(
  jobId,
  userId,
  assessmentData,
  'AI-Driven Talent Mapping'
);
```

### Direct Result Creation
```javascript
const personaProfile = {
  archetype: "The Helper",
  personality_summary: "Individu dengan kecenderungan sosial tinggi...",
  career_recommendations: ["HR Manager", "Teacher", "Social Worker"],
  strengths: ["Komunikasi", "Empati", "Teamwork"],
  development_areas: ["Technical Skills", "Time Management"]
};

const result = await archiveService.createAnalysisResult(
  userId,
  assessmentData,
  personaProfile,
  'AI-Driven Talent Mapping',
  'completed'
);
```

### Failed Result Creation
```javascript
const failedResult = await archiveService.createAnalysisResult(
  userId,
  incompleteData,
  null, // No persona profile
  'AI-Driven Talent Mapping',
  'failed',
  'Insufficient assessment data provided'
);
```

## Testing

### Unit Tests
```bash
npm test
```

### Integration Tests
```bash
# Test Archive Service integration
npm run test:archive

# Run integration examples
npm run test:archive:examples
```

### Manual Testing
```javascript
// Run all examples
const examples = require('./examples/archive-integration-examples');
await examples.runAllExamples();
```

## Error Handling

Semua methods menggunakan proper error handling:

```javascript
try {
  const result = await archiveService.createAnalysisResult(/* params */);
  console.log('Success:', result);
} catch (error) {
  console.error('Error:', error.message);
  
  // Check for specific HTTP errors
  if (error.response) {
    console.error('Status:', error.response.status);
    console.error('Data:', error.response.data);
  }
}
```

## Flow Integration

### Normal Assessment Flow
1. User submits assessment → Assessment Service
2. Assessment Service creates job → Archive Service (`/archive/jobs`)
3. Job queued → RabbitMQ
4. Analysis Worker processes → Creates result in Archive Service (`/archive/results`)
5. Job status updated → Archive Service

### Direct Result Creation (New)
1. Assessment Service dapat langsung membuat result → Archive Service (`/archive/results`)
2. Berguna untuk skenario khusus atau testing

## Monitoring

### Health Checks
```javascript
const isHealthy = await archiveService.checkHealth();
if (!isHealthy) {
  console.error('Archive Service is not available');
}
```

### Logging
Semua operations di-log dengan detail:
- Request parameters
- Response data
- Error details
- Performance metrics

## Best Practices

1. **Always check health** sebelum melakukan operations
2. **Use proper error handling** untuk semua async operations
3. **Log important events** untuk debugging dan monitoring
4. **Use environment variables** untuk configuration
5. **Test integration** secara regular dengan script yang disediakan

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check Archive Service is running on port 3002
   - Verify ARCHIVE_SERVICE_URL environment variable

2. **Authentication Failed**
   - Check INTERNAL_SERVICE_KEY is set correctly
   - Verify headers are being sent properly

3. **404 Not Found**
   - Verify endpoint paths use `/archive` prefix
   - Check Archive Service routes configuration

4. **Timeout Errors**
   - Archive Service might be overloaded
   - Check network connectivity
   - Consider increasing timeout in configuration
